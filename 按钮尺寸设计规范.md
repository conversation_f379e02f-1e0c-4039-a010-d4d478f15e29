# 按钮尺寸设计规范

## 📋 概述

本文档基于Figma设计系统，详细说明了按钮组件的尺寸规范、使用场景和设计原则。按钮作为用户界面中最重要的交互元素之一，需要遵循一致的设计标准以确保良好的用户体验。

## 🎯 设计原则

- **一致性**: 在整个产品中保持按钮样式的一致性
- **层次性**: 通过尺寸和颜色建立清晰的视觉层次
- **可访问性**: 确保按钮具有足够的点击区域和对比度
- **响应性**: 适应不同屏幕尺寸和设备类型

## 📏 尺寸规范

### 1. 小按钮 (Small Button)

| 属性 | 规格 |
|------|------|
| **圆角半径** | 12px |
| **使用场景** | 空间受限的场景，如表格操作、卡片内按钮 |
| **推荐宽度** | 短、中、长 |
| **优先级** | 低到中等 |

**适用场景：**
- 表格行内操作按钮
- 卡片内的次要操作
- 工具栏按钮
- 表单内的辅助按钮

### 2. 中按钮 (Medium Button)

| 属性 | 规格 |
|------|------|
| **圆角半径** | 15px |
| **使用场景** | 标准场景，空间充足时的推荐选择 |
| **推荐宽度** | 短、中、长 |
| **优先级** | 中等到高 |

**适用场景：**
- 表单提交按钮
- 对话框操作按钮
- 页面内的主要操作
- 导航按钮

### 3. 大按钮 (Large Button)

| 属性 | 规格 |
|------|------|
| **圆角半径** | 22px |
| **使用场景** | 页面级别的CTA（Call To Action）按钮 |
| **推荐宽度** | 中、长 |
| **优先级** | 最高 |

**适用场景：**
- 页面主要行动召唤
- 购买/支付按钮
- 注册/登录按钮
- 重要功能入口

## 📐 宽度规范

### 短宽度
- **用途**: 简短文字（1-2个字符）
- **场景**: 图标按钮、确认/取消按钮
- **示例**: "确定"、"取消"、"+"、"×"

### 中宽度
- **用途**: 标准文字长度（3-6个字符）
- **场景**: 大多数常规操作
- **示例**: "提交"、"保存"、"删除"、"编辑"

### 长宽度
- **用途**: 较长文字（7个字符以上）
- **场景**: 描述性操作、复合功能
- **示例**: "立即购买"、"添加到购物车"、"发送验证码"

## 🎨 视觉样式

### 实色样式 (Solid)
- **特点**: 填充背景色，视觉重量最重
- **用途**: 主要操作按钮
- **层次**: 最高优先级

### 线框样式 (Outline)
- **特点**: 透明背景，有边框
- **用途**: 次要操作按钮
- **层次**: 中等优先级

### 浅底样式 (Ghost)
- **特点**: 浅色背景，无边框
- **用途**: 辅助操作按钮
- **层次**: 较低优先级

## 🌈 颜色系统

| 颜色 | 用途 | 场景示例 |
|------|------|----------|
| **粉色(默认)** | 主要操作 | 提交、确认、保存 |
| **黄色** | 付费通知 | 购买、升级、付费功能 |
| **蓝色** | 下载/游戏 | 下载、安装、开始游戏 |
| **灰色** | 次要操作 | 取消、重置、查看详情 |
| **白色** | 深色背景 | 在深色主题下使用 |

## ⚡ 交互状态

### 正常状态 (Normal)
- **透明度**: 100%
- **描述**: 按钮的默认显示状态

### 按下状态 (Pressed)
- **透明度**: 70%
- **描述**: 用户点击时的视觉反馈

### 禁用状态 (Disabled)
- **透明度**: 40%
- **描述**: 按钮不可交互时的状态
- **注意**: 禁用按钮不应响应任何交互事件

## 🔧 组件构成

### 基础元素
1. **背板 (Container)**: 按钮的背景容器
2. **文字 (Text)**: 按钮的文字标签
3. **左图标 (Left Icon)**: 可选的左侧图标
4. **右图标 (Right Icon)**: 可选的右侧图标

### 配置选项
- 文字显示/隐藏
- 左图标显示/隐藏
- 右图标显示/隐藏
- 自定义文字内容

## 📱 响应式设计

### 移动端适配
- 确保按钮高度不小于44px（iOS）或48px（Android）
- 增加按钮间距以避免误触
- 考虑单手操作的便利性

### 桌面端优化
- 支持鼠标悬停状态
- 键盘导航支持
- 合理的按钮间距和对齐

## ✅ 使用指南

### 优先级排序
1. **大按钮**: 页面最重要的操作
2. **中按钮**: 重要但非关键的操作
3. **小按钮**: 辅助和次要操作

### 布局原则
- 主要操作按钮放在右侧或底部
- 危险操作使用警告色彩
- 保持按钮组的视觉平衡

### 文字规范
- 使用动词开头的简洁文字
- 避免使用模糊的词汇如"确定"
- 保持文字长度的一致性

## 🚫 注意事项

### 避免的做法
- 不要在同一界面使用过多不同尺寸的按钮
- 不要让次要按钮比主要按钮更突出
- 不要在小尺寸按钮中使用过长的文字

### 可访问性要求
- 确保按钮与背景有足够的对比度
- 为图标按钮提供文字说明
- 支持键盘导航和屏幕阅读器

## 📊 设计检查清单

- [ ] 按钮尺寸符合规范要求
- [ ] 圆角半径正确设置
- [ ] 颜色使用恰当
- [ ] 文字简洁明确
- [ ] 状态变化清晰
- [ ] 符合可访问性标准
- [ ] 在不同设备上测试通过

---

*本规范基于实际设计系统制定，如有疑问请联系设计团队。*
